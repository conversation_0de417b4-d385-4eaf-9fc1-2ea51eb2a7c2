# GitHub Actions Workflow is created for testing and preparing the plugin release in the following steps:
# - Validate Gradle Wrapper.
# - Run 'test' and 'verifyPlugin' tasks.
# - Run Qodana inspections.
# - Run the 'buildPlugin' task and prepare artifact for further tests.
# - Run the 'runPluginVerifier' task.
# - Create a draft release.
#
# The workflow is triggered on push and pull_request events.
#
# GitHub Actions reference: https://help.github.com/en/actions
#
## JBIJPPTPL

name: Build
on:
  # Trigger the workflow on pushes to only the 'main' branch (this avoids duplicate checks being run e.g., for dependabot pull requests)
  push:
    branches: [ main ]
  # Trigger the workflow on any pull request
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
jobs:

  # Prepare environment and build the plugin
  build:
    name: Build
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.properties.outputs.version }}
      changelog: ${{ steps.properties.outputs.changelog }}
      pluginVerifierHomeDir: ${{ steps.properties.outputs.pluginVerifierHomeDir }}
    steps:

      # Check out the current repository
      - name: Fetch Sources
        uses: actions/checkout@v4

      # Set up Java environment for the next steps
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      # Setup Gradle
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      # Set environment variables
      - name: Export Properties
        id: properties
        shell: bash
        run: |
          PROPERTIES="$(./gradlew properties --console=plain -q)"
          VERSION="$(echo "$PROPERTIES" | grep "^version:" | cut -f2- -d ' ')"
          CHANGELOG="$(./gradlew getChangelog --unreleased --no-header --console=plain -q)"

          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "pluginVerifierHomeDir=~/.pluginVerifier" >> $GITHUB_OUTPUT
          
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      # Build plugin
      - name: Build plugin
        run: ./gradlew buildPlugin

      # Prepare plugin archive content for creating artifact
      - name: Prepare Plugin Artifact
        id: artifact
        shell: bash
        run: |
          cd ${{ github.workspace }}/build/distributions
          FILENAME=`ls *.zip`
          unzip "$FILENAME" -d content

          echo "filename=${FILENAME:0:-4}" >> $GITHUB_OUTPUT

      # Store already-built plugin as an artifact for downloading
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.artifact.outputs.filename }}
          path: ./build/distributions/content/*/*

  # Run tests and upload a code coverage report
  test:
    name: Test
    needs: [ build ]
    runs-on: ubuntu-latest
    steps:

      # Check out the current repository
      - name: Fetch Sources
        uses: actions/checkout@v4

      # Set up Java environment for the next steps
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      # Setup Gradle
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      # Run tests
      - name: Run Tests
        run: ./gradlew check

      # Collect Tests Result of failed tests
      - name: Collect Tests Result
        if: ${{ failure() }}
        uses: actions/upload-artifact@v4
        with:
          name: tests-result
          path: ${{ github.workspace }}/build/reports/tests

      # Upload the Kover report to CodeCov
      - name: Upload Code Coverage Report
        uses: codecov/codecov-action@v5
        with:
          files: ${{ github.workspace }}/build/reports/kover/report.xml

  # Run Qodana inspections and provide report
  inspectCode:
    name: Inspect code
    needs: [ build ]
    runs-on: ubuntu-latest
    permissions:
      contents: write
      checks: write
      pull-requests: write
    steps:

      # Free GitHub Actions Environment Disk Space
      - name: Maximize Build Space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: false
          large-packages: false

      # Check out the current repository
      - name: Fetch Sources
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}  # to check out the actual pull request commit, not the merge commit
          fetch-depth: 0  # a full history is required for pull request analysis

      # Set up Java environment for the next steps
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      # Run Qodana inspections
      - name: Qodana - Code Inspection
        uses: JetBrains/qodana-action@v2025.1
        with:
          cache-default-branch-only: true

  # Run plugin structure verification along with IntelliJ Plugin Verifier
  verify:
    name: Verify plugin
    needs: [ build ]
    runs-on: ubuntu-latest
    steps:

      # Free GitHub Actions Environment Disk Space
      - name: Maximize Build Space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: false
          large-packages: false

      # Check out the current repository
      - name: Fetch Sources
        uses: actions/checkout@v4

      # Set up Java environment for the next steps
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      # Setup Gradle
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      # Cache Plugin Verifier IDEs
      - name: Setup Plugin Verifier IDEs Cache
        uses: actions/cache@v4
        with:
          path: ${{ needs.build.outputs.pluginVerifierHomeDir }}/ides
          key: plugin-verifier-${{ hashFiles('build/listProductsReleases.txt') }}

      # Run Verify Plugin task and IntelliJ Plugin Verifier tool
      - name: Run Plugin Verification tasks
        run: ./gradlew verifyPlugin -Dplugin.verifier.home.dir=${{ needs.build.outputs.pluginVerifierHomeDir }}

      # Collect Plugin Verifier Result
      - name: Collect Plugin Verifier Result
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: pluginVerifier-result
          path: ${{ github.workspace }}/build/reports/pluginVerifier

  # Prepare a draft release for GitHub Releases page for the manual verification
  # If accepted and published, release workflow would be triggered
  releaseDraft:
    name: Release draft
    if: github.event_name != 'pull_request'
    needs: [ build, test, inspectCode, verify ]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:

      # Check out the current repository
      - name: Fetch Sources
        uses: actions/checkout@v4

      # Remove old release drafts by using the curl request for the available releases with a draft flag
      - name: Remove Old Release Drafts
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh api repos/{owner}/{repo}/releases \
            --jq '.[] | select(.draft == true) | .id' \
            | xargs -I '{}' gh api -X DELETE repos/{owner}/{repo}/releases/{}

      # Create a new release draft which is not publicly visible and requires manual acceptance
      - name: Create Release Draft
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release create "v${{ needs.build.outputs.version }}" \
            --draft \
            --title "v${{ needs.build.outputs.version }}" \
            --notes "$(cat << 'EOM'
          ${{ needs.build.outputs.changelog }}
          EOM
          )"
