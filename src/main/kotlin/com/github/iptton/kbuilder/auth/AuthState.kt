package com.github.iptton.kbuilder.auth

/**
 * Represents the current authentication state
 */
enum class AuthState {
    NOT_AUTHENTICATED,
    DEVICE_FLOW_PENDING,
    AUTHENTICATED,
    TOKEN_EXPIRED,
    ERROR
}

/**
 * Device flow response from GitHub
 */
data class DeviceFlowResponse(
    val device_code: String,
    val user_code: String,
    val verification_uri: String,
    val expires_in: Int,
    val interval: Int
)

/**
 * Token response from GitHub
 */
data class TokenResponse(
    val access_token: String,
    val token_type: String,
    val scope: String,
    val refresh_token: String? = null,
    val expires_in: Int? = null
)

/**
 * Error response from GitHub API
 */
data class GitHubApiError(
    val error: String,
    val error_description: String? = null,
    val error_uri: String? = null
)

/**
 * Copilot internal token response
 */
data class CopilotTokenResponse(
    val token: String,
    val expires_at: Long? = null
)

/**
 * Authentication result wrapper
 */
sealed class AuthResult<out T> {
    data class Success<T>(val data: T) : AuthResult<T>()
    data class Error(val message: String, val cause: Throwable? = null) : AuthResult<Nothing>()
    data class Pending(val message: String) : AuthResult<Nothing>()
}
