package com.github.iptton.kbuilder.auth

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.google.gson.Gson
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.*

/**
 * GitHub Device Flow authentication service
 */
@Service
class GitHubDeviceFlowAuth {

    private val tokenStorage = service<TokenStorage>()

    private val logger = thisLogger()
    private val gson = Gson()
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    companion object {
        private const val CLIENT_ID = "Iv23ctfURkiMfJ4xr5mv"
        private const val DEVICE_CODE_URL = "https://github.com/login/device/code"
        private const val ACCESS_TOKEN_URL = "https://github.com/login/oauth/access_token"
        private const val SCOPE = "copilot"
        private const val USER_AGENT = "GitHub-Copilot-IntelliJ-Plugin/1.0"
    }

    private var currentDeviceFlow: DeviceFlowResponse? = null
    private var pollingJob: Job? = null

    /**
     * Start the device flow authentication process
     */
    suspend fun startDeviceFlow(): AuthResult<DeviceFlowResponse> = withContext(Dispatchers.IO) {
        try {
            logger.info("Starting GitHub device flow authentication")

            val requestBody = "client_id=$CLIENT_ID&scope=$SCOPE"
                .toRequestBody("application/x-www-form-urlencoded".toMediaType())

            val request = Request.Builder()
                .url(DEVICE_CODE_URL)
                .post(requestBody)
                .header("Accept", "application/json")
                .header("User-Agent", USER_AGENT)
                .header("editor-plugin-version", "copilot.vim/1.16.0")
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val deviceFlow = gson.fromJson(responseBody, DeviceFlowResponse::class.java)
                    currentDeviceFlow = deviceFlow
                    logger.info("Device flow started successfully. User code: ${deviceFlow.user_code}")
                    AuthResult.Success(deviceFlow)
                } else {
                    AuthResult.Error("Empty response from GitHub")
                }
            } else {
                val errorBody = response.body?.string()
                val error = try {
                    gson.fromJson(errorBody, GitHubApiError::class.java)
                } catch (e: Exception) {
                    GitHubApiError("unknown_error", "HTTP ${response.code}: ${response.message}")
                }
                logger.error("Device flow request failed: ${error.error} - ${error.error_description}")
                AuthResult.Error("${error.error}: ${error.error_description ?: "Unknown error"}")
            }
        } catch (e: IOException) {
            logger.error("Network error during device flow", e)
            AuthResult.Error("Network error: ${e.message}", e)
        } catch (e: Exception) {
            logger.error("Unexpected error during device flow", e)
            AuthResult.Error("Unexpected error: ${e.message}", e)
        }
    }

    /**
     * Start polling for the access token
     */
    suspend fun startPolling(
        deviceFlow: DeviceFlowResponse,
        onSuccess: (TokenResponse) -> Unit,
        onError: (String) -> Unit,
        onPending: (String) -> Unit
    ) {
        pollingJob?.cancel()
        pollingJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                val intervalMs = (deviceFlow.interval * 1000).toLong()
                val expiresAt = System.currentTimeMillis() + (deviceFlow.expires_in * 1000)

                while (isActive && System.currentTimeMillis() < expiresAt) {
                    delay(intervalMs)

                    val result = pollForToken(deviceFlow.device_code)
                    when (result) {
                        is AuthResult.Success -> {
                            logger.info("Authentication successful ${result}")
                            tokenStorage.storeGitHubToken(result.data.access_token, result.data.expires_in)
                            withContext(Dispatchers.Main) {
                                onSuccess(result.data)
                            }
                            return@launch
                        }
                        is AuthResult.Error -> {
                            if (result.message.contains("authorization_pending")) {
                                withContext(Dispatchers.Main) {
                                    onPending("Waiting for user authorization...")
                                }
                                continue
                            } else if (result.message.contains("slow_down")) {
                                // Increase polling interval
                                delay(intervalMs)
                                continue
                            } else {
                                logger.error("Polling failed: ${result.message}")
                                withContext(Dispatchers.Main) {
                                    onError(result.message)
                                }
                                return@launch
                            }
                        }
                        is AuthResult.Pending -> {
                            withContext(Dispatchers.Main) {
                                onPending(result.message)
                            }
                        }
                    }
                }

                // Timeout reached
                withContext(Dispatchers.Main) {
                    onError("Authentication timeout. Please try again.")
                }
            } catch (e: CancellationException) {
                logger.info("Polling cancelled")
            } catch (e: Exception) {
                logger.error("Error during polling", e)
                withContext(Dispatchers.Main) {
                    onError("Polling error: ${e.message}")
                }
            }
        }
    }

    /**
     * Poll for access token
     */
    private suspend fun pollForToken(deviceCode: String): AuthResult<TokenResponse> = withContext(Dispatchers.IO) {
        try {
            val requestBody = "client_id=$CLIENT_ID&device_code=$deviceCode&grant_type=urn:ietf:params:oauth:grant-type:device_code"
                .toRequestBody("application/x-www-form-urlencoded".toMediaType())

            val request = Request.Builder()
                .url(ACCESS_TOKEN_URL)
                .post(requestBody)
                .header("Accept", "application/json")
                .header("User-Agent", USER_AGENT)
                .build()

            val response = httpClient.newCall(request).execute()
            val responseBody = response.body?.string()

            if (response.isSuccessful && responseBody != null) {
                val tokenResponse = gson.fromJson(responseBody, TokenResponse::class.java)
                if (tokenResponse.access_token.isNullOrEmpty()) {
                    return@withContext AuthResult.Pending("Received empty access token")
                }
                AuthResult.Success(tokenResponse)
            } else {
                val error = try {
                    gson.fromJson(responseBody, GitHubApiError::class.java)
                } catch (e: Exception) {
                    GitHubApiError("unknown_error", "HTTP ${response.code}: ${response.message}")
                }

                when (error.error) {
                    "authorization_pending" -> AuthResult.Pending("Waiting for user authorization...")
                    "slow_down" -> AuthResult.Pending("Slowing down polling...")
                    else -> AuthResult.Error("${error.error}: ${error.error_description ?: "Unknown error"}")
                }
            }
        } catch (e: IOException) {
            AuthResult.Error("Network error: ${e.message}", e)
        } catch (e: Exception) {
            AuthResult.Error("Unexpected error: ${e.message}", e)
        }
    }

    /**
     * Check if user is currently authenticated
     */
    fun isAuthenticated(): Boolean {
        val token = tokenStorage.getGitHubToken()
        return token != null && !tokenStorage.isGitHubTokenExpired()
    }

    /**
     * Get current authentication state
     */
    fun getAuthState(): AuthState {
        return when {
            isAuthenticated() -> AuthState.AUTHENTICATED
            tokenStorage.getGitHubToken() != null && tokenStorage.isGitHubTokenExpired() -> AuthState.TOKEN_EXPIRED
            currentDeviceFlow != null -> AuthState.DEVICE_FLOW_PENDING
            else -> AuthState.NOT_AUTHENTICATED
        }
    }

    /**
     * Cancel ongoing authentication process
     */
    fun cancelAuthentication() {
        pollingJob?.cancel()
        currentDeviceFlow = null
        logger.info("Authentication process cancelled")
    }

    /**
     * Sign out and clear tokens
     */
    fun signOut() {
        cancelAuthentication()
        tokenStorage.clearAllTokens()
        logger.info("User signed out successfully")
    }
}
