package com.github.iptton.kbuilder.toolWindow

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.ui.content.ContentFactory
import com.github.iptton.kbuilder.MyBundle
import com.github.iptton.kbuilder.services.MyProjectService
import com.github.iptton.kbuilder.auth.GitHubDeviceFlowAuth
import com.github.iptton.kbuilder.auth.AuthState
import com.github.iptton.kbuilder.auth.AuthResult
import com.github.iptton.kbuilder.copilot.CopilotApiService
import com.github.iptton.kbuilder.copilot.CopilotApiResult
import com.github.iptton.kbuilder.copilot.CopilotRequestBuilder
import com.github.iptton.kbuilder.copilot.CopilotModel
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.Desktop
import java.awt.Dimension
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import java.awt.Insets
import java.net.URI
import javax.swing.*


class MyToolWindowFactory : ToolWindowFactory {

    init {
        thisLogger().warn("GitHub Copilot Plugin Tool Window")
    }

    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        val myToolWindow = MyToolWindow(toolWindow)
        val content = ContentFactory.getInstance().createContent(myToolWindow.getContent(), "GitHub Copilot", false)
        toolWindow.contentManager.addContent(content)
    }

    override fun shouldBeAvailable(project: Project) = true

    class MyToolWindow(toolWindow: ToolWindow) {

        private val logger = thisLogger()
        private val authService = service<GitHubDeviceFlowAuth>()
        private val copilotService = service<CopilotApiService>()
        private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
        private val workerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

        // UI Components
        private val statusLabel = JBLabel("Not authenticated")
        private val authButton = JButton("Authenticate with GitHub")
        private val signOutButton = JButton("Sign Out")
        private val testButton = JButton("Test Copilot API")
        private val getModelsButton = JButton("Get Available Models")

        private val promptArea = JBTextArea(3, 40)
        private val responseArea = JBTextArea(15, 40)
        private val sendButton = JButton("Send to Copilot")

        // Model selection
        private val modelComboBox = JComboBox<String>()
        private val refreshModelsButton = JButton("Refresh Models")

        init {
            setupUI()
            setupModelComboBox()
            updateAuthState()
        }

        fun getContent(): JComponent {
            return createMainPanel()
        }

        private fun createMainPanel(): JPanel {
            val mainPanel = JBPanel<JBPanel<*>>(BorderLayout())

            // Authentication Panel
            val authPanel = createAuthPanel()
            mainPanel.add(authPanel, BorderLayout.NORTH)

            // Chat Panel
            val chatPanel = createChatPanel()
            mainPanel.add(chatPanel, BorderLayout.CENTER)

            return mainPanel
        }

        private fun createAuthPanel(): JPanel {
            val panel = JBPanel<JBPanel<*>>(GridBagLayout())
            val gbc = GridBagConstraints()

            gbc.insets = Insets(5, 5, 5, 5)
            gbc.anchor = GridBagConstraints.WEST

            // Status
            gbc.gridx = 0; gbc.gridy = 0
            panel.add(JBLabel("Status:"), gbc)

            gbc.gridx = 1; gbc.gridy = 0
            gbc.weightx = 1.0
            gbc.fill = GridBagConstraints.HORIZONTAL
            panel.add(statusLabel, gbc)

            // Buttons
            gbc.gridx = 0; gbc.gridy = 1
            gbc.weightx = 0.0
            gbc.fill = GridBagConstraints.NONE
            panel.add(authButton, gbc)

            gbc.gridx = 1; gbc.gridy = 1
            panel.add(signOutButton, gbc)

            gbc.gridx = 2; gbc.gridy = 1
            panel.add(testButton, gbc)

            gbc.gridx = 3; gbc.gridy = 1
            panel.add(getModelsButton, gbc)

            return panel
        }

        private fun createChatPanel(): JPanel {
            val panel = JBPanel<JBPanel<*>>(BorderLayout())
            panel.border = BorderFactory.createTitledBorder("Chat with Copilot")

            // Model selection panel
            val modelPanel = JBPanel<JBPanel<*>>(GridBagLayout())
            val gbc = GridBagConstraints()
            gbc.insets = Insets(5, 5, 5, 5)
            gbc.anchor = GridBagConstraints.WEST

            gbc.gridx = 0; gbc.gridy = 0
            modelPanel.add(JBLabel("Model:"), gbc)

            gbc.gridx = 1; gbc.gridy = 0
            gbc.weightx = 1.0
            gbc.fill = GridBagConstraints.HORIZONTAL
            modelComboBox.preferredSize = Dimension(200, 25)
            modelPanel.add(modelComboBox, gbc)

            gbc.gridx = 2; gbc.gridy = 0
            gbc.weightx = 0.0
            gbc.fill = GridBagConstraints.NONE
            modelPanel.add(refreshModelsButton, gbc)

            // Prompt area
            val promptPanel = JBPanel<JBPanel<*>>(BorderLayout())
            promptPanel.add(JBLabel("Your message:"), BorderLayout.NORTH)

            promptArea.lineWrap = true
            promptArea.wrapStyleWord = true
            promptArea.text = "Hello! Can you help me write a simple Kotlin function?"

            val promptScroll = JBScrollPane(promptArea)
            promptScroll.preferredSize = Dimension(400, 80)
            promptPanel.add(promptScroll, BorderLayout.CENTER)
            promptPanel.add(sendButton, BorderLayout.SOUTH)

            val topPanel = JBPanel<JBPanel<*>>(BorderLayout())
            topPanel.add(modelPanel, BorderLayout.NORTH)
            topPanel.add(promptPanel, BorderLayout.CENTER)

            panel.add(topPanel, BorderLayout.NORTH)

            // Response area
            val responsePanel = JBPanel<JBPanel<*>>(BorderLayout())
            responsePanel.add(JBLabel("Copilot response:"), BorderLayout.NORTH)

            responseArea.isEditable = false
            responseArea.lineWrap = true
            responseArea.wrapStyleWord = true
            responseArea.text = "Responses will appear here..."

            val responseScroll = JBScrollPane(responseArea)
            responseScroll.preferredSize = Dimension(400, 300)
            responsePanel.add(responseScroll, BorderLayout.CENTER)

            panel.add(responsePanel, BorderLayout.CENTER)

            return panel
        }

        private fun setupUI() {
            // Authentication button
            authButton.addActionListener {
                authenticateWithGitHub()
            }

            // Sign out button
            signOutButton.addActionListener {
                signOut()
            }

            // Test button
            testButton.addActionListener {
                testCopilotConnection()
            }

            // Get models button
            getModelsButton.addActionListener {
                getAvailableModels()
            }

            // Refresh models button
            refreshModelsButton.addActionListener {
                refreshModels()
            }

            // Send button
            sendButton.addActionListener {
                sendMessageToCopilot()
            }

            // Enter key in prompt area
            promptArea.addKeyListener(object : java.awt.event.KeyAdapter() {
                override fun keyPressed(e: java.awt.event.KeyEvent) {
                    if (e.keyCode == java.awt.event.KeyEvent.VK_ENTER && e.isControlDown) {
                        sendMessageToCopilot()
                        e.consume()
                    }
                }
            })
        }

        private fun setupModelComboBox() {
            modelComboBox.addItem("Auto (Default)")
            modelComboBox.isEnabled = false
            refreshModelsButton.isEnabled = false

            // Load cached models if available
            loadCachedModels()
        }

        private fun loadCachedModels() {
            val cachedModels = copilotService.getCachedModelsSync()
            if (cachedModels != null) {
                updateModelComboBox(cachedModels)
            }
        }

        private fun updateModelComboBox(models: List<CopilotModel>) {
            modelComboBox.removeAllItems()
            modelComboBox.addItem("Auto (Default)")

            // 筛选policy.state为enabled的可用模型
            val enabledModels = models.filter { it.policy?.state == "enabled" }

            enabledModels.forEach { model ->
                // 显示更多有用的模型信息：ID、名称和供应商
                modelComboBox.addItem("${model.id} (${model.vendor ?: "unknown"})")
            }

            logger.info("Updated model combo box with ${enabledModels.size} enabled models out of ${models.size} total models")
        }

        private fun getSelectedModel(): String? {
            val selectedItem = modelComboBox.selectedItem as? String
            if (selectedItem == null || selectedItem == "Auto (Default)") {
                return null
            }

            // 我们现在存储的格式是 "模型名称 (提供商)"，需要查找对应的模型ID
            val modelName = selectedItem.substringBefore(" (")
            val cachedModels = copilotService.getCachedModelsSync()

            return cachedModels?.firstOrNull { it.name == modelName }?.id
        }

        private fun updateAuthState() {

            workerScope.launch {

                val authState = authService.getAuthState()

                when (authState) {
                    AuthState.NOT_AUTHENTICATED -> {
                        withContext(Dispatchers.Main) {
                            statusLabel.text = "Not authenticated"
                            authButton.isEnabled = true
                            signOutButton.isEnabled = false
                            testButton.isEnabled = false
                            getModelsButton.isEnabled = false
                            sendButton.isEnabled = false
                            modelComboBox.isEnabled = false
                            refreshModelsButton.isEnabled = false
                        }
                    }

                    AuthState.DEVICE_FLOW_PENDING -> {
                        withContext(Dispatchers.Main) {
                            statusLabel.text = "Authentication in progress..."
                            authButton.isEnabled = false
                            signOutButton.isEnabled = true
                            testButton.isEnabled = false
                            getModelsButton.isEnabled = false
                            sendButton.isEnabled = false
                            modelComboBox.isEnabled = false
                            refreshModelsButton.isEnabled = false
                        }
                    }

                    AuthState.AUTHENTICATED -> {
                        withContext(Dispatchers.Main) {
                            statusLabel.text = "Authenticated ✓"
                            authButton.isEnabled = false
                            signOutButton.isEnabled = true
                            testButton.isEnabled = true
                            getModelsButton.isEnabled = true
                            sendButton.isEnabled = true
                            modelComboBox.isEnabled = true
                            refreshModelsButton.isEnabled = true
                        }

                        // Auto-fetch models if not cached
                        if (!copilotService.hasCachedModels()) {
                            autoFetchModels()
                        }
                    }

                    AuthState.TOKEN_EXPIRED -> {
                        withContext(Dispatchers.Main) {
                            statusLabel.text = "Token expired - please re-authenticate"
                            authButton.isEnabled = true
                            signOutButton.isEnabled = true
                            testButton.isEnabled = false
                            getModelsButton.isEnabled = false
                            sendButton.isEnabled = false
                            modelComboBox.isEnabled = false
                            refreshModelsButton.isEnabled = false
                        }
                    }

                    AuthState.ERROR -> {
                        withContext(Dispatchers.Main) {
                            statusLabel.text = "Authentication error"
                            authButton.isEnabled = true
                            signOutButton.isEnabled = true
                            testButton.isEnabled = false
                            getModelsButton.isEnabled = false
                            sendButton.isEnabled = false
                            modelComboBox.isEnabled = false
                            refreshModelsButton.isEnabled = false
                        }
                    }
                }
            }
        }

        private fun authenticateWithGitHub() {
            workerScope.launch {
                try {
                    responseArea.text = "Starting GitHub authentication...\n"

                    val deviceFlowResult = authService.startDeviceFlow()
                    when (deviceFlowResult) {
                        is AuthResult.Success -> {
                            val deviceFlow = deviceFlowResult.data
                            appendResponseAerea("Please visit: ${deviceFlow.verification_uri}\n")
                            appendResponseAerea("And enter code: ${deviceFlow.user_code}\n\n")

                            // Try to open browser
                            try {
                                if (Desktop.isDesktopSupported()) {
                                    Desktop.getDesktop().browse(URI(deviceFlow.verification_uri))
                                    appendResponseAerea("Browser opened automatically.\n")
                                }
                            } catch (e: Exception) {
                                logger.warn("Could not open browser", e)
                                appendResponseAerea("Please open the URL manually.\n")
                            }

                            updateAuthState()

                            // Start polling
                            authService.startPolling(
                                deviceFlow,
                                onSuccess = { tokenResponse ->
                                    SwingUtilities.invokeLater {
                                        appendResponseAerea("Authentication successful! ✓\n")
                                        updateAuthState()
                                        // Auto-fetch models after successful authentication
                                        autoFetchModels()
                                    }
                                },
                                onError = { error ->
                                    SwingUtilities.invokeLater {
                                        appendResponseAerea("Authentication failed: $error\n")
                                        updateAuthState()
                                    }
                                },
                                onPending = { message ->
                                    SwingUtilities.invokeLater {
                                        statusLabel.text = message
                                    }
                                }
                            )
                        }
                        is AuthResult.Error -> {
                            appendResponseAerea("Failed to start authentication: ${deviceFlowResult.message}\n")
                            updateAuthState()
                        }
                        is AuthResult.Pending -> {
                            appendResponseAerea("Authentication pending: ${deviceFlowResult.message}\n")
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error during authentication", e)
                    appendResponseAerea("Error: ${e.message}\n")
                    updateAuthState()
                }
            }
        }

        private fun signOut() {
            authService.signOut()
            copilotService.clearModelsCache()
            modelComboBox.removeAllItems()
            modelComboBox.addItem("Auto (Default)")
            setResponseAerea("Signed out successfully.\n")
            updateAuthState()
        }

        private fun appendResponseAerea(text: String) {
            coroutineScope.launch {
                responseArea.append(text)
                responseArea.caretPosition = responseArea.document.length
            }
        }

        private fun setResponseAerea(text: String) {
            coroutineScope.launch {
                responseArea.text += text
                responseArea.caretPosition = responseArea.document.length
            }
        }

        private fun autoFetchModels() {
            coroutineScope.launch {
                try {
                    logger.info("Auto-fetching models after authentication")
                    val result = copilotService.getCachedModels(forceRefresh = false)
                    when (result) {
                        is CopilotApiResult.Success -> {
                            SwingUtilities.invokeLater {
                                updateModelComboBox(result.data)
                                appendResponseAerea("Models loaded automatically (${result.data.size} available)\n")
                            }
                        }
                        is CopilotApiResult.Error -> {
                            logger.warn("Failed to auto-fetch models: ${result.message}")
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error auto-fetching models", e)
                }
            }
        }

        private fun refreshModels() {
            coroutineScope.launch {
                try {
                    appendResponseAerea("Refreshing models...\n")

                    val result = copilotService.getCachedModels(forceRefresh = true)
                    when (result) {
                        is CopilotApiResult.Success -> {
                            SwingUtilities.invokeLater {
                                updateModelComboBox(result.data)
                                appendResponseAerea("Models refreshed successfully! (${result.data.size} available)\n")
                            }
                        }
                        is CopilotApiResult.Error -> {
                            appendResponseAerea("Failed to refresh models: ${result.message}\n")
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error refreshing models", e)
                    appendResponseAerea("Error refreshing models: ${e.message}\n")
                }
            }
        }

        private fun testCopilotConnection() {
            coroutineScope.launch {
                try {
                    setResponseAerea("Testing Copilot connection...\n")

                    val result = copilotService.simpleChat("Hello! Please respond with a simple greeting.")
                    when (result) {
                        is CopilotApiResult.Success -> {
                            appendResponseAerea("✓ Copilot connection successful!\n")
                            appendResponseAerea("Response: ${result.data}\n\n")
                        }
                        is CopilotApiResult.Error -> {
                            appendResponseAerea("✗ Copilot connection failed: ${result.message}\n\n")
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error testing Copilot connection", e)
                    appendResponseAerea("Error: ${e.message}\n\n")
                }
            }
        }

        private fun getAvailableModels() {
            coroutineScope.launch {
                try {
                    appendResponseAerea("Fetching available models...\n")

                    val result = copilotService.getCachedModels(forceRefresh = false)
                    when (result) {
                        is CopilotApiResult.Success -> {
                            SwingUtilities.invokeLater {
                                updateModelComboBox(result.data)
                            }
                            appendResponseAerea("Available models:\n")
                            result.data.forEach { model ->
                                appendResponseAerea("- ${model.id} (GitHub})\n")
                            }
                            appendResponseAerea("\n")
                        }
                        is CopilotApiResult.Error -> {
                            appendResponseAerea("Failed to get models: ${result.message}\n\n")
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error getting models", e)
                    appendResponseAerea("Error: ${e.message}\n\n")
                }
            }
        }

        private fun sendMessageToCopilot() {
            val message = promptArea.text.trim()
            if (message.isEmpty()) {
                return
            }

            coroutineScope.launch {
                try {
                    // Get selected model
                    val selectedModel = getSelectedModel()
                    val modelText = if (selectedModel != null) " (using $selectedModel)" else ""

                    appendResponseAerea("You: $message\n\n")
                    appendResponseAerea("Copilot is thinking$modelText...\n")

                    // Clear the prompt area
                    promptArea.text = ""

                    val result = copilotService.simpleChat(
                        message = message,
                        systemPrompt = "You are a helpful coding assistant. Provide clear, concise, and accurate responses.",
                        model = selectedModel
                    )

                    when (result) {
                        is CopilotApiResult.Success -> {
                            responseArea.text = responseArea.text.replace("Copilot is thinking...\n", "")
                            appendResponseAerea("Copilot: ${result.data}\n\n")
                            appendResponseAerea("---\n\n")
                        }
                        is CopilotApiResult.Error -> {
                            responseArea.text = responseArea.text.replace("Copilot is thinking...\n", "")
                            appendResponseAerea("Error: ${result.message}\n\n")
                        }
                    }

                    // Scroll to bottom
                    SwingUtilities.invokeLater {
                        responseArea.caretPosition = responseArea.document.length
                    }

                } catch (e: Exception) {
                    logger.error("Error sending message to Copilot", e)
                    appendResponseAerea("Error: ${e.message}\n\n")
                }
            }
        }
    }
}
