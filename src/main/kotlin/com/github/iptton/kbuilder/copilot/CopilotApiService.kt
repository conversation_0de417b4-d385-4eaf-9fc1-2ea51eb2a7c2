package com.github.iptton.kbuilder.copilot

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.github.iptton.kbuilder.auth.AuthResult
import com.github.iptton.kbuilder.auth.TokenStorage
import com.google.gson.Gson
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.time.Instant
import java.util.concurrent.TimeUnit
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.*

/**
 * GitHub Copilot API service for chat and text completions
 */
@Service
class CopilotApiService {

    private val tokenManager = service<CopilotTokenManager>()
    private val tokenStorage = service<TokenStorage>()

    private val logger = thisLogger()
    private val gson = Gson()
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    // Model caching
    private val modelsCache = ConcurrentHashMap<String, CachedModels>()
    private var lastFetchTime: Long = 0
    private var lastTokenHash: String? = null

    companion object {
        private const val COPILOT_BASE_URL = "https://api.githubcopilot.com"
        private const val MODELS_ENDPOINT = "$COPILOT_BASE_URL/models"
        private const val CHAT_COMPLETIONS_ENDPOINT = "$COPILOT_BASE_URL/chat/completions"
        private const val TEXT_COMPLETIONS_ENDPOINT = "$COPILOT_BASE_URL/completions"

        private const val USER_AGENT = "GitHub-Copilot-IntelliJ-Plugin/1.0"
        private const val EDITOR_VERSION = "IntelliJ/2024.2"
        private const val COPILOT_INTEGRATION_ID = "vscode-chat"

        // Cache settings
        private const val CACHE_DURATION_SECONDS = 3600 // 1 hour
        private const val CACHE_KEY = "copilot_models"
    }

    /**
     * Cached models data
     */
    data class CachedModels(
        val models: List<CopilotModel>,
        val fetchTime: Long,
        val tokenHash: String
    ) {
        fun isExpired(): Boolean {
            return Instant.now().epochSecond - fetchTime > CACHE_DURATION_SECONDS
        }
    }

    /**
     * Get cached models if available and valid, otherwise fetch new ones
     */
    suspend fun getCachedModels(forceRefresh: Boolean = false): CopilotApiResult<List<CopilotModel>> = withContext(Dispatchers.IO) {
        try {
            val currentTokenHash = getCurrentTokenHash()
            if (currentTokenHash == null) {
                return@withContext CopilotApiResult.Error("No valid authentication token available")
            }

            // Check if we have valid cached models
            val cachedModels = modelsCache[CACHE_KEY]
            if (!forceRefresh && cachedModels != null &&
                !cachedModels.isExpired() &&
                cachedModels.tokenHash == currentTokenHash) {

                logger.debug("Using cached models (${cachedModels.models.size} models)")
                return@withContext CopilotApiResult.Success(cachedModels.models)
            }

            // Fetch new models
            logger.info("Fetching models from API (cache ${if (forceRefresh) "force refresh" else "expired/invalid"})")
            fetchAndCacheModels(currentTokenHash)

        } catch (e: Exception) {
            logger.error("Error getting cached models", e)
            CopilotApiResult.Error("Failed to get models: ${e.message}", cause = e)
        }
    }

    /**
     * Get available models from Copilot API (original method, now used internally)
     */
    suspend fun getModels(): CopilotApiResult<ModelsResponse> = withContext(Dispatchers.IO) {
        try {
            val tokenResult = tokenManager.getValidCopilotToken()
            if (tokenResult !is AuthResult.Success<*>) {
                return@withContext CopilotApiResult.Error("Failed to get Copilot token: ${(tokenResult as AuthResult.Error).message}")
            }

            val request = Request.Builder()
                .url(MODELS_ENDPOINT)
                .get()
                .header("Authorization", "Bearer ${tokenResult.data}")
                .header("Editor-Version", EDITOR_VERSION)
                .header("Content-Type", "application/json")
                .header("Copilot-Integration-Id", COPILOT_INTEGRATION_ID)
                .header("User-Agent", USER_AGENT)
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val modelsResponse = gson.fromJson(responseBody, ModelsResponse::class.java)
                    logger.info("Successfully retrieved ${modelsResponse.data.size} models")
                    CopilotApiResult.Success(modelsResponse)
                } else {
                    CopilotApiResult.Error("Empty response from models endpoint")
                }
            } else {
                handleErrorResponse(response, "Failed to get models")
            }
        } catch (e: IOException) {
            logger.error("Network error getting models", e)
            CopilotApiResult.Error("Network error: ${e.message}", cause = e)
        } catch (e: Exception) {
            logger.error("Unexpected error getting models", e)
            CopilotApiResult.Error("Unexpected error: ${e.message}", cause = e)
        }
    }

    /**
     * Fetch models from API and cache them
     */
    private suspend fun fetchAndCacheModels(tokenHash: String): CopilotApiResult<List<CopilotModel>> {
        return when (val result = getModels()) {
            is CopilotApiResult.Success -> {
                val models = result.data.data.filter { it.available() }
                val cachedModels = CachedModels(
                    models = models,
                    fetchTime = Instant.now().epochSecond,
                    tokenHash = tokenHash
                )

                modelsCache[CACHE_KEY] = cachedModels
                lastFetchTime = cachedModels.fetchTime
                lastTokenHash = tokenHash

                logger.info("Successfully cached ${models.size} models")
                CopilotApiResult.Success(models)
            }
            is CopilotApiResult.Error -> {
                logger.error("Failed to fetch models: ${result.message}")
                result
            }
        }
    }

    /**
     * Get current token hash for cache validation
     */
    private fun getCurrentTokenHash(): String? {
        val githubToken = tokenStorage.getGitHubToken()
        val copilotToken = tokenStorage.getCopilotToken()

        if (githubToken == null || copilotToken == null) {
            return null
        }

        if (tokenStorage.isGitHubTokenExpired() || tokenStorage.isCopilotTokenExpired()) {
            return null
        }

        // Create a hash from both tokens to detect token changes
        return "${githubToken.hashCode()}_${copilotToken.hashCode()}"
    }

    /**
     * Clear the models cache
     */
    fun clearModelsCache() {
        modelsCache.clear()
        lastFetchTime = 0
        lastTokenHash = null
        logger.info("Models cache cleared")
    }

    /**
     * Check if models are cached and valid
     */
    fun hasCachedModels(): Boolean {
        val currentTokenHash = getCurrentTokenHash() ?: return false
        val cachedModels = modelsCache[CACHE_KEY] ?: return false

        return !cachedModels.isExpired() && cachedModels.tokenHash == currentTokenHash
    }

    /**
     * Get cached models without fetching (returns null if not cached or expired)
     */
    fun getCachedModelsSync(): List<CopilotModel>? {
        val currentTokenHash = getCurrentTokenHash() ?: return null
        val cachedModels = modelsCache[CACHE_KEY] ?: return null

        return if (!cachedModels.isExpired() && cachedModels.tokenHash == currentTokenHash) {
            cachedModels.models
        } else {
            null
        }
    }

    /**
     * Send a chat completion request
     */
    suspend fun chatCompletion(request: ChatCompletionRequest): CopilotApiResult<ChatCompletionResponse> = withContext(Dispatchers.IO) {
        try {
            val tokenResult = tokenManager.getValidCopilotToken()
            if (tokenResult !is AuthResult.Success<*>) {
                return@withContext CopilotApiResult.Error("Failed to get Copilot token: ${(tokenResult as AuthResult.Error).message}")
            }

            val requestBody = gson.toJson(request)
                .toRequestBody("application/json".toMediaType())

            val httpRequest = Request.Builder()
                .url(CHAT_COMPLETIONS_ENDPOINT)
                .post(requestBody)
                .header("Authorization", "Bearer ${tokenResult.data}")
                .header("Editor-Version", EDITOR_VERSION)
                .header("Content-Type", "application/json")
                .header("Copilot-Integration-Id", COPILOT_INTEGRATION_ID)
                .header("User-Agent", USER_AGENT)
                .build()

            val response = httpClient.newCall(httpRequest).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val completionResponse = gson.fromJson(responseBody, ChatCompletionResponse::class.java)
                    logger.info("Chat completion successful, ${completionResponse.choices.size} choices returned")
                    CopilotApiResult.Success(completionResponse)
                } else {
                    CopilotApiResult.Error("Empty response from chat completions endpoint")
                }
            } else {
                handleErrorResponse(response, "Chat completion failed")
            }
        } catch (e: IOException) {
            logger.error("Network error during chat completion", e)
            CopilotApiResult.Error("Network error: ${e.message}", cause = e)
        } catch (e: Exception) {
            logger.error("Unexpected error during chat completion", e)
            CopilotApiResult.Error("Unexpected error: ${e.message}", cause = e)
        }
    }

    /**
     * Send a text completion request
     */
    suspend fun textCompletion(request: TextCompletionRequest): CopilotApiResult<TextCompletionResponse> = withContext(Dispatchers.IO) {
        try {
            val tokenResult = tokenManager.getValidCopilotToken()
            if (tokenResult !is AuthResult.Success<*>) {
                return@withContext CopilotApiResult.Error("Failed to get Copilot token: ${(tokenResult as AuthResult.Error).message}")
            }

            val requestBody = gson.toJson(request)
                .toRequestBody("application/json".toMediaType())

            val httpRequest = Request.Builder()
                .url(TEXT_COMPLETIONS_ENDPOINT)
                .post(requestBody)
                .header("Authorization", "Bearer ${tokenResult.data}")
                .header("Editor-Version", EDITOR_VERSION)
                .header("Content-Type", "application/json")
                .header("Copilot-Integration-Id", COPILOT_INTEGRATION_ID)
                .header("User-Agent", USER_AGENT)
                .build()

            val response = httpClient.newCall(httpRequest).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val completionResponse = gson.fromJson(responseBody, TextCompletionResponse::class.java)
                    logger.info("Text completion successful, ${completionResponse.choices.size} choices returned")
                    CopilotApiResult.Success(completionResponse)
                } else {
                    CopilotApiResult.Error("Empty response from text completions endpoint")
                }
            } else {
                handleErrorResponse(response, "Text completion failed")
            }
        } catch (e: IOException) {
            logger.error("Network error during text completion", e)
            CopilotApiResult.Error("Network error: ${e.message}", cause = e)
        } catch (e: Exception) {
            logger.error("Unexpected error during text completion", e)
            CopilotApiResult.Error("Unexpected error: ${e.message}", cause = e)
        }
    }

    /**
     * Convenience method for simple chat completion
     */
    suspend fun simpleChat(
        message: String,
        systemPrompt: String? = null,
        temperature: Double = 0.1,
        maxTokens: Int? = null,
        model: String? = null
    ): CopilotApiResult<String> {
        val request = CopilotRequestBuilder.chatCompletion(
            userMessage = message,
            systemMessage = systemPrompt,
            temperature = temperature,
            maxTokens = maxTokens
        ).copy(model = model)

        return when (val result = chatCompletion(request)) {
            is CopilotApiResult.Success -> {
                val firstChoice = result.data.choices.firstOrNull()
                val content = firstChoice?.message?.content
                if (content != null) {
                    CopilotApiResult.Success(content)
                } else {
                    CopilotApiResult.Error("No content in response")
                }
            }
            is CopilotApiResult.Error -> result
        }
    }

    /**
     * Convenience method for simple text completion
     */
    suspend fun simpleTextCompletion(
        prompt: String,
        maxTokens: Int? = null,
        temperature: Double = 0.1
    ): CopilotApiResult<String> {
        val request = CopilotRequestBuilder.textCompletion(
            prompt = prompt,
            maxTokens = maxTokens,
            temperature = temperature
        )

        return when (val result = textCompletion(request)) {
            is CopilotApiResult.Success -> {
                val firstChoice = result.data.choices.firstOrNull()
                val text = firstChoice?.text
                if (text != null) {
                    CopilotApiResult.Success(text)
                } else {
                    CopilotApiResult.Error("No text in response")
                }
            }
            is CopilotApiResult.Error -> result
        }
    }

    /**
     * Handle error responses from the API
     */
    private fun handleErrorResponse(response: Response, context: String): CopilotApiResult.Error {
        val errorBody = response.body?.string()

        return try {
            val apiError = gson.fromJson(errorBody, CopilotApiError::class.java)
            logger.error("$context: ${apiError.error.message}")
            CopilotApiResult.Error(
                message = apiError.error.message,
                code = apiError.error.code
            )
        } catch (e: Exception) {
            logger.error("$context: HTTP ${response.code} - $errorBody")
            when (response.code) {
                401 -> CopilotApiResult.Error("Authentication failed. Please re-authenticate.", "401")
                403 -> CopilotApiResult.Error("Access denied. Check your Copilot subscription.", "403")
                429 -> CopilotApiResult.Error("Rate limit exceeded. Please try again later.", "429")
                500 -> CopilotApiResult.Error("Copilot service error. Please try again later.", "500")
                else -> CopilotApiResult.Error("HTTP ${response.code}: ${response.message}", response.code.toString())
            }
        }
    }
}
