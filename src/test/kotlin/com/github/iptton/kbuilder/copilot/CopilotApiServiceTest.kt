package com.github.iptton.kbuilder.copilot

import org.junit.Test
import org.junit.Assert.*

class CopilotApiServiceTest {

    @Test
    fun testCachedModelsDataClass() {
        val models = listOf(
            CopilotModel(
                id = "gpt-4",
                `object` = "model",
                owned_by = "openai"
            ),
            CopilotModel(
                id = "gpt-3.5-turbo",
                `object` = "model", 
                owned_by = "openai"
            )
        )
        
        val cachedModels = CopilotApiService.CachedModels(
            models = models,
            fetchTime = System.currentTimeMillis() / 1000,
            tokenHash = "test_hash"
        )
        
        assertEquals(2, cachedModels.models.size)
        assertEquals("gpt-4", cachedModels.models[0].id)
        assertEquals("gpt-3.5-turbo", cachedModels.models[1].id)
        assertFalse(cachedModels.isExpired()) // Should not be expired immediately
    }
    
    @Test
    fun testCachedModelsExpiration() {
        val models = listOf(
            CopilotModel(
                id = "gpt-4",
                `object` = "model",
                owned_by = "openai"
            )
        )
        
        // Create cached models with old timestamp (more than 1 hour ago)
        val oldTimestamp = (System.currentTimeMillis() / 1000) - 3700 // 1 hour and 2 minutes ago
        val cachedModels = CopilotApiService.CachedModels(
            models = models,
            fetchTime = oldTimestamp,
            tokenHash = "test_hash"
        )
        
        assertTrue(cachedModels.isExpired()) // Should be expired
    }
}
