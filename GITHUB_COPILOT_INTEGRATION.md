# GitHub Copilot Integration

This IntelliJ IDEA plugin provides GitHub Device Flow authentication and GitHub Copilot API integration.

## Features

### 1. GitHub Device Flow Authentication
- Secure OAuth authentication using GitHub's Device Flow
- Automatic token management and refresh
- Secure token storage using IntelliJ's credential store

### 2. GitHub Copilot API Integration
- Complete integration with GitHub Copilot's LLM APIs
- Support for chat completions and text completions
- Automatic token acquisition and management
- Model discovery and selection

### 3. User Interface
- Integrated tool window for easy access
- Authentication status display
- Interactive chat interface with Copilot
- Model listing and testing capabilities

## Setup and Usage

### Prerequisites
- IntelliJ IDEA 2024.2 or later
- Active GitHub Copilot subscription
- Internet connection for authentication and API calls

### Authentication Process

1. **Open the Tool Window**
   - Navigate to `View` → `Tool Windows` → `MyToolWindow`
   - Or use the tool window tab if already visible

2. **Authenticate with GitHub**
   - Click the "Authenticate with <PERSON>it<PERSON><PERSON>" button
   - The plugin will display a device code and verification URL
   - Your browser should open automatically to GitHub's device flow page
   - If not, manually visit the displayed URL
   - Enter the device code shown in the plugin
   - Authorize the application in your browser
   - The plugin will automatically detect successful authentication

3. **Verify Connection**
   - Once authenticated, click "Test Copilot API" to verify the connection
   - Click "Get Available Models" to see what models are available

### Using the Chat Interface

1. **Send Messages**
   - Type your message in the prompt area
   - Click "Send to Copilot" or press `Ctrl+Enter`
   - Responses will appear in the response area

2. **Example Prompts**
   - "Help me write a Kotlin function to sort a list"
   - "Explain how to implement a binary search algorithm"
   - "Review this code and suggest improvements: [paste your code]"

## API Endpoints Used

The plugin integrates with the following GitHub Copilot API endpoints:

### 1. Get Copilot Internal Token
```
GET https://api.github.com/copilot_internal/v2/token
Authorization: token ${github_access_token}
Accept: application/json
```

### 2. Get Supported Models
```
GET https://api.githubcopilot.com/models
Authorization: Bearer ${copilot_internal_token}
Editor-Version: IntelliJ/2024.2
Content-Type: application/json
Copilot-Integration-Id: vscode-chat
```

### 3. Chat Completions
```
POST https://api.githubcopilot.com/chat/completions
Authorization: Bearer ${copilot_internal_token}
Editor-Version: IntelliJ/2024.2
Content-Type: application/json
Copilot-Integration-Id: vscode-chat

Body:
{
  "messages": [{"role": "user", "content": "Your message"}],
  "intent": false,
  "n": 1,
  "temperature": 0.1,
  "stream": false
}
```

## Architecture

### Core Components

1. **Authentication Layer**
   - `GitHubDeviceFlowAuth`: Handles OAuth device flow
   - `TokenStorage`: Secure token storage and management
   - `AuthState`: Authentication state management

2. **Copilot API Layer**
   - `CopilotTokenManager`: Manages Copilot internal tokens
   - `CopilotApiService`: Main API service for completions
   - `CopilotModels`: Data models for API requests/responses

3. **UI Layer**
   - `MyToolWindowFactory`: Tool window with chat interface
   - Real-time authentication status updates
   - Interactive chat functionality

### Security Features

- **Secure Token Storage**: Uses IntelliJ's built-in credential store
- **Automatic Token Refresh**: Handles token expiration automatically
- **Error Handling**: Comprehensive error handling for network and API issues
- **Rate Limiting**: Respects GitHub's API rate limits

## Configuration

### Client ID
The plugin uses the GitHub Copilot app client ID: `Iv23ctfURkiMfJ4xr5mv`

### Scopes
Required OAuth scope: `copilot`

### Token Expiration
- GitHub tokens: Configurable (typically long-lived)
- Copilot internal tokens: ~1 hour (automatically refreshed)

## Troubleshooting

### Authentication Issues

1. **"Access denied" error**
   - Ensure you have an active GitHub Copilot subscription
   - Check that your GitHub account has Copilot access

2. **Token expired**
   - Click "Sign Out" and re-authenticate
   - Tokens are automatically refreshed when possible

3. **Network errors**
   - Check your internet connection
   - Verify firewall settings allow HTTPS connections to GitHub

### API Issues

1. **"Rate limit exceeded"**
   - Wait a few minutes before making more requests
   - GitHub has rate limits for API usage

2. **"Model not found"**
   - Use "Get Available Models" to see current model list
   - Some models may not be available in all regions

### UI Issues

1. **Tool window not visible**
   - Go to `View` → `Tool Windows` → `MyToolWindow`
   - Check if the plugin is properly installed and enabled

2. **Buttons disabled**
   - Check authentication status
   - Some features require successful authentication

## Development

### Building the Plugin
```bash
./gradlew buildPlugin
```

### Running Tests
```bash
./gradlew test
```

### Dependencies
- OkHttp 4.12.0 for HTTP requests
- Gson 2.10.1 for JSON parsing
- Kotlin Coroutines for async operations

## License

This plugin is part of the kbuilder project. See the main README for license information.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the plugin logs in IntelliJ's log files
3. Create an issue in the project repository
