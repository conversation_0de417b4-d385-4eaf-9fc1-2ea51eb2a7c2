main:
  push:
    - docker:
        image: gradle:7.6-jdk17
        volumes:
          - /root/.gradle:copy-on-write
      services:
        - docker
      stages:
        - name: gradle build
          script:
            - ./gradlew build
        - name: docker login
          script:
            - docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
        - name: docker build
          script:
            - docker build -t ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_COMMIT} .
        - name: docker push
          script:
            - docker push ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_COMMIT}